import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/translation_confidence_indicator.dart';
import 'package:culture_connect/widgets/translation/voice_translation_offline_indicator.dart';
import 'package:culture_connect/widgets/translation/cultural_context_indicator.dart';
import 'package:culture_connect/widgets/translation/cultural_context_card.dart';
import 'package:culture_connect/widgets/translation/cultural_context_dialog.dart';

/// A widget that displays translated text
class TranslatedTextDisplay extends StatefulWidget {
  /// The translation model
  final ImageTextTranslationModel translation;

  /// Whether to show the original text
  final bool showOriginalText;

  /// Callback when the copy button is tapped
  final VoidCallback? onCopyTranslation;

  /// Callback when the share button is tapped
  final VoidCallback? onShareTranslation;

  /// Callback when the favorite button is tapped
  final VoidCallback? onToggleFavorite;

  /// Creates a new translated text display
  const TranslatedTextDisplay({
    super.key,
    required this.translation,
    this.showOriginalText = true,
    this.onCopyTranslation,
    this.onShareTranslation,
    this.onToggleFavorite,
  });

  @override
  State<TranslatedTextDisplay> createState() => _TranslatedTextDisplayState();
}

class _TranslatedTextDisplayState extends State<TranslatedTextDisplay> {
  bool _showCopiedMessage = false;

  @override
  Widget build(BuildContext context) {
    // Determine text direction for target language
    final isTargetRTL = widget.translation.isTargetRTL;
    final targetTextDirection = widget.translation.targetTextDirection;

    return Directionality(
      textDirection: targetTextDirection,
      child: Card(
        margin: const EdgeInsets.all(16),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment:
                isTargetRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              // Header with language info
              _buildHeader(),

              const Divider(height: 24),

              // Original text (if available and enabled)
              if (widget.showOriginalText &&
                  widget.translation.recognizedText != null) ...[
                _buildOriginalText(),
                const Divider(height: 24),
              ],

              // Translated text
              _buildTranslatedText(),

              // Cultural context card
              if (widget.translation.culturalContext != null &&
                  widget.translation.culturalContext!.hasContextInformation &&
                  widget.translation.showCulturalContext) ...[
                const SizedBox(height: 16),
                CulturalContextCard(
                  culturalContext: widget.translation.culturalContext!,
                  showFullContent: false,
                  onTap: () {
                    _showCulturalContextDialog(context);
                  },
                ),
              ],

              const SizedBox(height: 16),

              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ).animate().fadeIn(duration: 300.ms).slideY(
            begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad),
      ),
    );
  }

  /// Build the header with language info
  Widget _buildHeader() {
    // Determine text direction for target language
    final isTargetRTL = widget.translation.isTargetRTL;

    return Row(
      children: [
        // Source language
        if (widget.translation.sourceLanguage != null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.all(Radius.circular(8)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getLanguageFlag(widget.translation.sourceLanguage!),
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  _getLanguageName(widget.translation.sourceLanguage!),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Arrow - use appropriate direction based on RTL
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Icon(
              isTargetRTL ? Icons.arrow_back : Icons.arrow_forward,
              size: 16,
              color: Colors.grey,
            ),
          ),
        ],

        // Target language
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(25),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getLanguageFlag(widget.translation.targetLanguage),
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                _getLanguageName(widget.translation.targetLanguage),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),

        const Spacer(),

        // Offline indicator
        if (widget.translation.isOfflineTranslation)
          VoiceTranslationOfflineIndicator(
            isOffline: true,
            confidence: widget.translation.confidence?.overallScore ?? 1.0,
            size: 16,
            showLabel: false,
          ),

        const SizedBox(width: 8),

        // Confidence indicator
        if (widget.translation.confidence != null)
          TranslationConfidenceIndicator(
            confidence: widget.translation.confidence!,
            compact: true,
            showLabel: false,
          ),

        // Cultural context indicator
        if (widget.translation.culturalContext != null &&
            widget.translation.culturalContext!.hasContextInformation &&
            widget.translation.showCulturalContext)
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: CulturalContextIndicator(
              culturalContext: widget.translation.culturalContext!,
              compact: true,
              showLabel: false,
              onTap: () {
                _showCulturalContextDialog(context);
              },
            ),
          ),
      ],
    );
  }

  /// Build the original text section
  Widget _buildOriginalText() {
    // Determine text direction for source language
    final isSourceRTL = widget.translation.isSourceRTL;
    final sourceTextDirection = widget.translation.sourceTextDirection;
    final sourceTextAlign = widget.translation.sourceTextAlign;

    return Column(
      crossAxisAlignment:
          isSourceRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          'Original Text',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Directionality(
            textDirection: sourceTextDirection,
            child: Text(
              widget.translation.recognizedText!,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: sourceTextAlign,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the translated text section
  Widget _buildTranslatedText() {
    if (widget.translation.status == ImageTextTranslationStatus.inProgress) {
      return const Center(
        child: Column(
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
            SizedBox(height: 16),
            Text(
              'Translating...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      );
    } else if (widget.translation.status == ImageTextTranslationStatus.failed) {
      return Center(
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Translation failed',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.translation.errorMessage ?? 'Unknown error',
              style: TextStyle(
                fontSize: 14,
                color: Colors.red[700],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else if (widget.translation.translatedText == null) {
      return const Center(
        child: Text(
          'No translation available',
          style: TextStyle(
            fontSize: 16,
            fontStyle: FontStyle.italic,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Determine text direction for target language
    final isTargetRTL = widget.translation.isTargetRTL;
    final targetTextDirection = widget.translation.targetTextDirection;
    final targetTextAlign = widget.translation.targetTextAlign;

    return Column(
      crossAxisAlignment:
          isTargetRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Text(
          'Translation',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withAlpha(13),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: AppTheme.primaryColor.withAlpha(51),
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              Directionality(
                textDirection: targetTextDirection,
                child: Text(
                  widget.translation.translatedText!,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                  textAlign: targetTextAlign,
                ),
              ),

              // Copied message
              if (_showCopiedMessage)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: const BorderRadius.all(Radius.circular(4)),
                    ),
                    child: const Text(
                      'Copied!',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ).animate().fadeIn().then(delay: 1.seconds).fadeOut(),
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build the action buttons
  Widget _buildActionButtons() {
    // Determine text direction for target language
    final isTargetRTL = widget.translation.isTargetRTL;

    return Row(
      mainAxisAlignment:
          isTargetRTL ? MainAxisAlignment.start : MainAxisAlignment.end,
      children: [
        // Copy button
        IconButton(
          icon: const Icon(Icons.copy),
          onPressed: _copyTranslation,
          tooltip: 'Copy translation',
        ),

        // Share button
        if (widget.onShareTranslation != null)
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: widget.onShareTranslation,
            tooltip: 'Share translation',
          ),

        // Favorite button
        if (widget.onToggleFavorite != null)
          IconButton(
            icon: Icon(
              widget.translation.isFavorite
                  ? Icons.favorite
                  : Icons.favorite_border,
              color: widget.translation.isFavorite ? Colors.red : null,
            ),
            onPressed: widget.onToggleFavorite,
            tooltip: widget.translation.isFavorite
                ? 'Remove from favorites'
                : 'Add to favorites',
          ),
      ],
    );
  }

  /// Copy the translation to the clipboard
  void _copyTranslation() {
    if (widget.translation.translatedText != null) {
      Clipboard.setData(
          ClipboardData(text: widget.translation.translatedText!));

      setState(() {
        _showCopiedMessage = true;
      });

      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showCopiedMessage = false;
          });
        }
      });

      if (widget.onCopyTranslation != null) {
        widget.onCopyTranslation!();
      }
    }
  }

  /// Show the cultural context dialog
  void _showCulturalContextDialog(BuildContext context) {
    if (widget.translation.culturalContext == null ||
        !widget.translation.culturalContext!.hasContextInformation) {
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: double.infinity,
          constraints: const BoxConstraints(
            maxWidth: 500,
            maxHeight: 600,
          ),
          child: CulturalContextDialog(
            culturalContext: widget.translation.culturalContext!,
          ),
        ),
      ),
    );
  }

  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }

  /// Get the name for a language code
  String _getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }
}
